apiVersion: trainer.kubeflow.org/v1alpha1
kind: ClusterTrainingRuntime
metadata:
  name: evalscope-model-evaluation
  labels:
    app: evalscope-perf-test
    purpose: model-evaluation
    framework: evalscope
spec:
  mlPolicy:
    numNodes: 1
    torch:
      numProcPerNode: 1

  template:
    metadata:
      labels:
        app: evalscope-perf-test
    spec:
      # JobSet 完成后 30 分钟自动清理
      ttlSecondsAfterFinished: 1800

      replicatedJobs:
      - name: node
        replicas: 1
        template:
          metadata:
            labels:
              trainer.kubeflow.org/trainjob-ancestor-step: trainer
              app: evalscope-perf-test
              purpose: model-evaluation
          spec:
            # Job 完成后 30 分钟自动清理
            ttlSecondsAfterFinished: 1800
            parallelism: 1
            completions: 1

            template:
              metadata:
                labels:
                  trainer.kubeflow.org/trainjob-ancestor-step: trainer
                  app: evalscope-perf-test
                  purpose: model-evaluation
              spec:
                restartPolicy: Never
                # 卷定义
                volumes:
                - name: results
                  emptyDir:
                    sizeLimit: 1Gi

                containers:
                - name: node
                  image: evalscope-perf:v1.0
                  # 默认命令和参数（可被 TrainJob 覆盖）
                  command: ["python"]
                  args: ["-m", "evalscope.perf", "--help"]

                  # 卷挂载
                  volumeMounts:
                  - name: results
                    mountPath: /tmp/results

                  # 默认资源配置
                  resources:
                    requests:
                      cpu: "1"
                      memory: "2Gi"
                    limits:
                      cpu: "4"
                      memory: "8Gi"

                  # 默认环境变量
                  env:
                  - name: RESULTS_DIR
                    value: "/tmp/results"
