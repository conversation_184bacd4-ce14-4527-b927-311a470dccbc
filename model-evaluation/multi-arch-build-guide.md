# EvalScope 多架构镜像构建指南

## 📋 概述

新的 `build-image.sh` 脚本支持多架构镜像构建，可以在 ARM64 MacBook 上构建出同时支持 AMD64 和 ARM64 架构的 Docker 镜像，确保在不同架构的 Kubernetes 集群中都能正常运行。

## 🚀 快速开始

### 1. 基本多架构构建

```bash
# 构建支持 AMD64 和 ARM64 的多架构镜像
./build-image.sh

# 等效于
./build-image.sh --platforms linux/amd64,linux/arm64
```

### 2. 单架构构建 (向后兼容)

```bash
# 仅构建当前架构 (向后兼容模式)
./build-image.sh --single-arch

# 仅构建 AMD64 架构
./build-image.sh --platforms linux/amd64

# 仅构建 ARM64 架构  
./build-image.sh --platforms linux/arm64
```

### 3. 自定义配置

```bash
# 指定镜像仓库和标签
./build-image.sh \
  --registry registry.example.com \
  --tag v2.0

# 仅构建不推送
./build-image.sh --no-push

# 预演模式 (显示命令但不执行)
./build-image.sh --dry-run
```

## 📖 命令行参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `-h, --help` | 显示帮助信息 | `./build-image.sh --help` |
| `-p, --platforms` | 指定构建架构 | `--platforms linux/amd64,linux/arm64` |
| `-s, --single-arch` | 单架构模式 (向后兼容) | `--single-arch` |
| `-r, --registry` | 指定镜像仓库地址 | `--registry registry.example.com` |
| `-t, --tag` | 指定镜像标签 | `--tag v2.0` |
| `--no-push` | 仅构建不推送 | `--no-push` |
| `--dry-run` | 预演模式 | `--dry-run` |

## 🏗️ 多架构构建原理

### Docker Buildx

脚本使用 Docker Buildx 进行多架构构建：

1. **检查 Buildx**: 自动检查 Docker Buildx 是否可用
2. **创建 Builder**: 创建专用的 `evalscope-builder` 实例
3. **跨平台构建**: 使用 QEMU 模拟不同架构
4. **Manifest 创建**: 自动创建多架构 manifest

### 构建流程

```mermaid
graph TD
    A[开始构建] --> B{检查 Buildx}
    B -->|可用| C[创建/使用 Builder]
    B -->|不可用| D[回退到单架构模式]
    C --> E[启动 Builder]
    E --> F[多架构构建]
    F --> G[推送到仓库]
    G --> H[创建 Manifest]
    H --> I[构建完成]
    D --> J[单架构构建]
    J --> K[推送到仓库]
    K --> I
```

## 🔧 环境要求

### 必需组件

1. **Docker Desktop** (推荐) 或 Docker Engine 20.10+
2. **Docker Buildx** (通常随 Docker Desktop 安装)
3. **QEMU** (用于跨架构模拟，Docker Desktop 自带)

### 检查环境

```bash
# 检查 Docker 版本
docker version

# 检查 Buildx 是否可用
docker buildx version

# 查看可用的 builder
docker buildx ls

# 查看支持的架构
docker buildx inspect --bootstrap
```

### 安装 Buildx (如果需要)

```bash
# 方法 1: 更新 Docker Desktop (推荐)
# 下载最新版 Docker Desktop

# 方法 2: 手动安装 Buildx 插件
mkdir -p ~/.docker/cli-plugins
curl -L https://github.com/docker/buildx/releases/latest/download/buildx-v0.12.0-darwin-arm64 -o ~/.docker/cli-plugins/docker-buildx
chmod +x ~/.docker/cli-plugins/docker-buildx
```

## 📊 使用场景

### 场景 1: ARM64 MacBook 开发者

```bash
# 在 ARM64 MacBook 上构建多架构镜像
./build-image.sh

# 结果: 同时支持 ARM64 和 AMD64 的镜像
# 可以在 x86_64 Kubernetes 集群中正常运行
```

### 场景 2: CI/CD 流水线

```bash
# 在 CI 中构建多架构镜像
./build-image.sh \
  --registry $CI_REGISTRY \
  --tag $CI_COMMIT_SHA \
  --platforms linux/amd64,linux/arm64
```

### 场景 3: 快速测试

```bash
# 仅构建当前架构进行快速测试
./build-image.sh --single-arch --no-push
```

### 场景 4: 生产部署

```bash
# 构建生产版本
./build-image.sh \
  --registry production-registry.com \
  --tag stable \
  --platforms linux/amd64,linux/arm64
```

## 🎯 最佳实践

### 1. 开发阶段

```bash
# 快速迭代 - 单架构构建
./build-image.sh --single-arch --no-push

# 本地测试
docker run --rm evalscope-perf:v1.0 --help
```

### 2. 测试阶段

```bash
# 构建多架构但不推送
./build-image.sh --no-push

# 验证构建结果
docker buildx imagetools inspect evalscope-perf:v1.0
```

### 3. 生产发布

```bash
# 完整多架构构建和推送
./build-image.sh \
  --registry your-production-registry.com \
  --tag $(git describe --tags)
```

## 🔍 故障排查

### 1. Buildx 不可用

**问题**: `Docker buildx 不可用`

**解决方案**:
```bash
# 更新 Docker Desktop 到最新版本
# 或手动安装 buildx 插件 (见上文)

# 验证安装
docker buildx version
```

### 2. 跨架构构建失败

**问题**: `exec format error` 或构建超时

**解决方案**:
```bash
# 确保 QEMU 可用
docker run --rm --privileged multiarch/qemu-user-static --reset -p yes

# 重新创建 builder
docker buildx rm evalscope-builder
./build-image.sh
```

### 3. 推送失败

**问题**: `unauthorized` 或 `access denied`

**解决方案**:
```bash
# 登录镜像仓库
docker login your-registry.com

# 检查权限
docker push your-registry.com/test:latest
```

### 4. 内存不足

**问题**: 构建过程中内存不足

**解决方案**:
```bash
# 增加 Docker Desktop 内存限制
# 或分别构建不同架构
./build-image.sh --platforms linux/amd64
./build-image.sh --platforms linux/arm64
```

## 📈 性能优化

### 1. 构建缓存

```bash
# 使用构建缓存加速
export DOCKER_BUILDKIT=1

# 或在 Dockerfile 中使用缓存挂载
RUN --mount=type=cache,target=/root/.cache/pip pip install evalscope[all]
```

### 2. 并行构建

```bash
# Docker Buildx 自动并行构建不同架构
# 无需额外配置
```

### 3. 镜像大小优化

```bash
# 使用多阶段构建 (已在 Dockerfile 中实现)
# 清理不必要的文件和缓存
```

## 🌟 高级用法

### 1. 自定义 Builder

```bash
# 创建自定义 builder
docker buildx create \
  --name custom-builder \
  --driver docker-container \
  --driver-opt network=host \
  --use

# 使用自定义 builder
BUILDER_NAME=custom-builder ./build-image.sh
```

### 2. 远程构建

```bash
# 使用远程 Docker 引擎构建
docker buildx create \
  --name remote-builder \
  --driver docker \
  --driver-opt host=tcp://remote-docker:2376 \
  --use
```

### 3. 构建缓存共享

```bash
# 使用注册表缓存
./build-image.sh \
  --cache-from type=registry,ref=your-registry.com/cache \
  --cache-to type=registry,ref=your-registry.com/cache,mode=max
```

这个多架构构建方案确保了在任何架构的开发环境中都能构建出兼容目标 Kubernetes 集群的镜像！
