# EvalScope 模型推理性能压测 - 配置总结


用 `evalscope perf` 命令进行压力测试。

## 🔄 主要调整内容

### 1. 名称和标识调整

| 组件 | 原名称 | 新名称 | 用途 |
|------|--------|--------|------|
| ClusterTrainingRuntime | `evalscope-model-evaluation` | `evalscope-stress-test` | 模型推理性能压测 |
| TrainJob | `evalscope-perf-test` | `evalscope-stress-test` | 压力测试任务 |
| 标签 | `purpose: model-evaluation` | `purpose: stress-test` | 明确压测用途 |

### 2. 压测参数优化

```yaml
# 压测核心参数
env:
  - name: PARALLEL
    value: "1 10 50 100 200"  # 并发数列表
  - name: NUMBER
    value: "10 20 100 200 400"  # 每个并发的请求数
  - name: DATASET
    value: "random"  # 随机数据集
  - name: EXTRA_ARGS
    value: '{"ignore_eos": true}'  # 忽略结束token
```

### 3. Token 配置

```yaml
# 固定输入输出长度进行压测
- name: MAX_TOKENS
  value: "1024"  # 最大输出token数
- name: MIN_TOKENS
  value: "1024"  # 最小输出token数
- name: MIN_PROMPT_LENGTH
  value: "1024"  # 最小输入prompt长度
- name: MAX_PROMPT_LENGTH
  value: "1024"  # 最大输入prompt长度
```

### 4. 命令行参数

```yaml
# 使用 evalscope perf 命令
args:
  - "--parallel"
  - "$(PARALLEL)"
  - "--number"
  - "$(NUMBER)"
  - "--model"
  - "$(MODEL)"
  - "--url"
  - "$(URL)"
  - "--api"
  - "$(API)"
  - "--dataset"
  - "$(DATASET)"
  - "--max-tokens"
  - "$(MAX_TOKENS)"
  - "--min-tokens"
  - "$(MIN_TOKENS)"
  - "--min-prompt-length"
  - "$(MIN_PROMPT_LENGTH)"
  - "--max-prompt-length"
  - "$(MAX_PROMPT_LENGTH)"
  - "--tokenizer-path"
  - "$(TOKENIZER_PATH)"
  - "--outputs-dir"
  - "/tmp/results"
  - "--name"
  - "kubeflow-stress-test"
  - "--extra-args"
  - "$(EXTRA_ARGS)"
```

## 📊 压测指标

压测完成后会生成以下文件：

- **`benchmark_summary.json`** - 主要性能指标摘要
- **`benchmark_percentile.json`** - 百分位延迟数据
- **`benchmark_args.json`** - 测试参数记录

### 关键指标说明

| 指标 | 英文名称 | 说明 |
|------|----------|------|
| QPS | Request throughput | 每秒请求数 |
| 平均延迟 | Average latency | 端到端平均延迟 |
| TTFT | Time to First Token | 首token时间 |
| Token吞吐量 | Output token throughput | 每秒输出token数 |
| 百分位延迟 | Percentile latency | P50, P90, P95, P99 |

## 🚀 使用方式

### 1. 标准压测

```bash
# 部署完整压测配置
kubectl apply -f evalscope-cluster-training-runtime.yaml
kubectl apply -f evalscope-trainjob.yaml
```

### 2. 快速测试

```bash
# 部署快速测试配置（较小规模）
kubectl apply -f evalscope-cluster-training-runtime.yaml
kubectl apply -f quick-start-example.yaml
```

### 3. 简化配置

```bash
# 使用 emptyDir 的简化配置
kubectl apply -f evalscope-kubeflow-emptydir.yaml
```

## 🔧 自定义配置

### 压测规模调整

```yaml
# 轻量级压测
PARALLEL: "1 5 10"
NUMBER: "5 10 20"
MAX_TOKENS: "128"

# 中等规模压测
PARALLEL: "1 10 50"
NUMBER: "10 50 100"
MAX_TOKENS: "512"

# 大规模压测
PARALLEL: "1 10 50 100 200"
NUMBER: "10 20 100 200 400"
MAX_TOKENS: "1024"
```

### API 服务配置

```yaml
# 本地服务
URL: "http://127.0.0.1:8801/v1/chat/completions"

# 集群内服务
URL: "http://model-service.default.svc.cluster.local:8801/v1/chat/completions"

# 外部服务
URL: "https://api.example.com/v1/chat/completions"
```

## 📁 文件结构

```
model-evaluation/
├── evalscope-cluster-training-runtime.yaml  # 压测运行时
├── evalscope-trainjob.yaml                  # 标准压测配置
├── quick-start-example.yaml                 # 快速测试配置
├── evalscope-kubeflow-emptydir.yaml         # 简化配置
├── Dockerfile                               # 压测镜像
├── build-image.sh                           # 镜像构建
├── deploy-evalscope.sh                      # 部署脚本
├── monitor-evalscope.sh                     # 监控脚本
├── cleanup-evalscope.sh                     # 清理脚本
├── README-evalscope.md                      # 使用指南
├── EVALSCOPE_PERF_README.md                 # 详细说明
├── custom-image-guide.md                    # 镜像指南
└── SUMMARY.md                               # 本文档
```

## 🎯 最佳实践

1. **先小规模测试**: 使用 `quick-start-example.yaml` 验证功能
2. **逐步增加压力**: 从小并发开始，逐步增加到目标并发
3. **固定Token长度**: 使用固定的输入输出长度获得稳定结果
4. **监控资源使用**: 确保集群资源充足
5. **保存结果**: 及时获取压测结果文件

## 🔍 故障排查

1. **镜像问题**: 确保 `evalscope-perf:v1.0` 镜像可用
2. **网络连通性**: 验证到模型服务的网络连接
3. **资源不足**: 检查 CPU/内存资源配置
4. **API兼容性**: 确认模型服务支持 OpenAI API 格式

这样调整后的配置专门针对模型推理性能压测，符合 EvalScope 的压测功能设计。
