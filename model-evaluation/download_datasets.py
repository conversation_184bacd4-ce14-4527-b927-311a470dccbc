#!/usr/bin/env python3
"""
数据集预下载脚本
用于在 Docker 镜像构建时预下载所需的数据集
"""

import os
import sys
from modelscope import dataset_snapshot_download


def download_hc3_chinese():
    """下载 HC3-Chinese 数据集"""
    
    try:
        print("开始下载 HC3-Chinese 数据集...")
        
        # 创建缓存目录
        cache_dir = '/mnt/workspace/.cache/modelscope'
        dataset_dir = '/mnt/workspace/.cache/modelscope/hub/datasets/AI-ModelScope'
        
        os.makedirs(dataset_dir, exist_ok=True)
        print(f"缓存目录已创建: {dataset_dir}")
        
        # 下载数据集
        dataset_snapshot_download(
            'AI-ModelScope/HC3-Chinese', 
            cache_dir=cache_dir
        )
        
        print("✅ HC3-Chinese 数据集下载成功!")
        return True
        
    except Exception as e:
        print(f"❌ 下载 HC3-Chinese 数据集失败: {e}")
        return False


def download_other_datasets():
    """下载其他常用数据集（可扩展）"""
    datasets = [
        # 可以在这里添加其他需要预下载的数据集
        # ('dataset-name', 'cache-path'),
    ]
    
    success_count = 0
    for dataset_name, cache_path in datasets:
        try:
            print(f"开始下载数据集: {dataset_name}")
            # 这里可以根据不同的数据集类型使用不同的下载方法
            # dataset_snapshot_download(dataset_name, cache_dir=cache_path)
            print(f"✅ {dataset_name} 下载成功!")
            success_count += 1
        except Exception as e:
            print(f"❌ 下载 {dataset_name} 失败: {e}")
    
    return success_count


def main():
    """主函数"""
    print("=" * 50)
    print("数据集预下载脚本")
    print("=" * 50)
    
    success = True
    
    # 下载 HC3-Chinese 数据集
    if not download_hc3_chinese():
        success = False
    
    # 下载其他数据集（如果有的话）
    # download_other_datasets()
    
    print("=" * 50)
    if success:
        print("🎉 所有数据集下载完成!")
        sys.exit(0)
    else:
        print("⚠️  部分数据集下载失败，请检查日志")
        sys.exit(1)


if __name__ == "__main__":
    main()
