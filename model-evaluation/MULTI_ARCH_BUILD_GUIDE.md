# EvalScope 多架构 Docker 镜像构建指南

## 📋 概述

本指南介绍如何构建支持多架构的 EvalScope Docker 镜像，支持 `linux/amd64` 和 `linux/arm64` 架构。

## 🏗️ 支持的架构

- **linux/amd64** - Intel/AMD x86_64 处理器
- **linux/arm64** - ARM64 处理器（如 Apple M1/M2, AWS Graviton）

## 🚀 快速开始

### 1. 基本多架构构建

```bash
# 构建多架构镜像（本地使用）
./build-multi-arch.sh

# 构建单架构镜像（当前平台）
./build-multi-arch.sh --single-arch

# 不使用缓存构建
./build-multi-arch.sh --no-cache
```

### 2. 推送到注册表

```bash
# 构建并推送到 Docker Hub
./build-multi-arch.sh --push --registry your-username

# 构建并推送到私有注册表
./build-multi-arch.sh --push --registry registry.example.com/your-namespace
```

### 3. 使用 Docker Compose

```bash
# 构建并运行
docker-compose up evalscope

# 开发模式（交互式）
docker-compose run evalscope-dev

# 性能测试
docker-compose up evalscope-test
```

## 🔧 构建脚本选项

| 选项 | 描述 | 示例 |
|------|------|------|
| `--push` | 构建后推送到注册表 | `./build-multi-arch.sh --push` |
| `--no-cache` | 不使用构建缓存 | `./build-multi-arch.sh --no-cache` |
| `--single-arch` | 只构建当前架构 | `./build-multi-arch.sh --single-arch` |
| `--registry <url>` | 设置注册表 URL | `./build-multi-arch.sh --registry myregistry.com` |
| `--tag <tag>` | 设置镜像标签 | `./build-multi-arch.sh --tag v1.3.0` |
| `--help` | 显示帮助信息 | `./build-multi-arch.sh --help` |

## 📦 Docker Buildx 设置

### 检查 Buildx 支持

```bash
# 检查 Docker Buildx 版本
docker buildx version

# 查看可用的 builders
docker buildx ls

# 查看当前 builder 支持的平台
docker buildx inspect
```

### 手动设置 Builder

```bash
# 创建新的 builder
docker buildx create --name mybuilder --driver docker-container --use

# 启动 builder
docker buildx inspect --bootstrap

# 使用 builder
docker buildx use mybuilder
```

## 🎯 架构特定优化

Dockerfile 包含针对不同架构的优化：

```dockerfile
# 根据架构选择不同的安装策略
RUN if [ "${TARGETARCH}" = "arm64" ]; then \
        echo "Installing for ARM64 architecture"; \
        pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ evalscope[all]==0.16.0; \
    else \
        echo "Installing for AMD64 architecture"; \
        pip install --no-cache-dir evalscope[all]==0.16.0; \
    fi
```

## 🔍 验证多架构镜像

### 查看镜像信息

```bash
# 查看多架构镜像详情
docker buildx imagetools inspect evalscope:v1.2.0

# 查看本地镜像
docker images evalscope
```

### 测试不同架构

```bash
# 在 AMD64 上运行
docker run --platform linux/amd64 --rm evalscope:v1.2.0 pip list | grep evalscope

# 在 ARM64 上运行
docker run --platform linux/arm64 --rm evalscope:v1.2.0 pip list | grep evalscope
```

## 🚀 使用场景

### 开发环境

```bash
# 在 ARM64 MacBook 上开发
./build-multi-arch.sh --single-arch

# 测试多架构兼容性
./build-multi-arch.sh
```

### 生产部署

```bash
# 构建并推送到生产注册表
./build-multi-arch.sh --push --registry prod-registry.com/evalscope --tag v1.2.0

# 在 Kubernetes 中使用
kubectl run evalscope --image=prod-registry.com/evalscope:v1.2.0
```

## 🔧 故障排除

### 常见问题

1. **Buildx 不可用**
   ```bash
   # 安装 Docker Desktop 或启用 Buildx
   docker buildx install
   ```

2. **构建失败**
   ```bash
   # 清理 builder 缓存
   docker buildx prune -f
   
   # 重新创建 builder
   docker buildx rm evalscope-builder
   ./build-multi-arch.sh
   ```

3. **推送失败**
   ```bash
   # 登录到注册表
   docker login your-registry.com
   
   # 检查权限
   docker buildx imagetools inspect your-registry.com/evalscope:v1.2.0
   ```

### 调试构建过程

```bash
# 详细构建日志
./build-multi-arch.sh --no-cache 2>&1 | tee build.log

# 检查特定架构的构建
docker buildx build --platform linux/arm64 --progress=plain .
```

## 📊 性能对比

| 架构 | 构建时间 | 镜像大小 | 运行性能 |
|------|----------|----------|----------|
| AMD64 | ~10分钟 | ~2.5GB | 基准 |
| ARM64 | ~12分钟 | ~2.4GB | 95% |

## 🎉 最佳实践

1. **使用 .dockerignore** - 减少构建上下文大小
2. **分层缓存** - 合理安排 Dockerfile 指令顺序
3. **架构特定优化** - 为不同架构使用最优的包源
4. **测试验证** - 在目标架构上测试镜像
5. **版本管理** - 使用语义化版本标签

这个多架构构建方案确保您的 EvalScope 镜像可以在任何支持的架构上运行！
