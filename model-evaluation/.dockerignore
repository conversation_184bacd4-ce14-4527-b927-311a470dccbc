# Git 相关
.git
.gitignore
.gitattributes

# 文档文件
*.md
README*
CHANGELOG*
LICENSE*

# 构建产物和缓存
build/
dist/
*.egg-info/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
.pytest_cache/
.coverage
.tox/
.cache/

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 测试结果
results/
test-results/
coverage/

# Docker 相关
Dockerfile*
docker-compose*
.dockerignore

# 构建脚本（除了需要的）
build-*.sh
*.sh
!build-multi-arch.sh

# 配置文件
.env
.env.local
.env.*.local

# 数据文件
data/
datasets/
models/
*.pkl
*.h5
*.hdf5
