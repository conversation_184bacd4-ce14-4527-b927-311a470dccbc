# EvalScope 模型推理性能压测 - 快速开始示例
# 这是一个简化的配置，适合快速测试

apiVersion: trainer.kubeflow.org/v1alpha1
kind: TrainJob
metadata:
  name: evalscope-quick-test
  labels:
    app: evalscope-perf-test
    test-type: quick-start
spec:
  runtimeRef:
    apiGroup: trainer.kubeflow.org
    kind: ClusterTrainingRuntime
    name: evalscope-stress-test

  trainer:
    image: evalscope-perf:v1.0
    
    # 快速压测配置 - 较小的并发和请求数
    args:
      - "--parallel"
      - "$(PARALLEL)"
      - "--number"
      - "$(NUMBER)"
      - "--model"
      - "$(MODEL)"
      - "--url"
      - "$(URL)"
      - "--api"
      - "$(API)"
      - "--dataset"
      - "$(DATASET)"
      - "--max-tokens"
      - "$(MAX_TOKENS)"
      - "--min-tokens"
      - "$(MIN_TOKENS)"
      - "--min-prompt-length"
      - "$(MIN_PROMPT_LENGTH)"
      - "--max-prompt-length"
      - "$(MAX_PROMPT_LENGTH)"
      - "--tokenizer-path"
      - "$(TOKENIZER_PATH)"
      - "--outputs-dir"
      - "/tmp/results"
      - "--name"
      - "quick-test"
    
    # 快速测试环境变量 - 较小的压测规模
    env:
      # 压测核心参数 - 快速测试配置
      - name: PARALLEL
        value: "1 5 10"  # 较小的并发数
      - name: NUMBER
        value: "5 10 20"  # 较少的请求数
      - name: URL
        value: "http://127.0.0.1:8801/v1/chat/completions"
      - name: API
        value: "openai"
      - name: MODEL
        value: "Qwen2.5-0.5B-Instruct"
      - name: DATASET
        value: "random"
      
      # Token 配置 - 较小的token数量
      - name: MAX_TOKENS
        value: "128"  # 较小的输出token数
      - name: MIN_TOKENS
        value: "128"
      - name: MIN_PROMPT_LENGTH
        value: "128"  # 较小的输入prompt长度
      - name: MAX_PROMPT_LENGTH
        value: "128"
      - name: TOKENIZER_PATH
        value: "Qwen/Qwen2.5-0.5B-Instruct"
      
      # 系统环境变量
      - name: PYTHONUNBUFFERED
        value: "1"
    
    # 较小的资源配置
    resourcesPerNode:
      requests:
        cpu: "1"
        memory: "2Gi"
      limits:
        cpu: "2"
        memory: "4Gi"

  # 标签
  labels:
    experiment: "evalscope-quick-test"
    version: "v1.0"
    
  # 注解
  annotations:
    description: "EvalScope快速压测示例 - 适合初次测试"
    test-purpose: "快速验证压测功能"
