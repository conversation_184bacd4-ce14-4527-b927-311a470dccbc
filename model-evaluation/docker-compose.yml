version: '3.8'

services:
  evalscope:
    build:
      context: .
      dockerfile: Dockerfile
      platforms:
        - linux/amd64
        - linux/arm64
      args:
        - BUILDKIT_INLINE_CACHE=1
    image: evalscope:latest
    container_name: evalscope-perf
    working_dir: /workspace
    volumes:
      - ./results:/workspace/results
      - ./data:/workspace/data
    environment:
      - MODELSCOPE_CACHE=/mnt/workspace/.cache/modelscope
    command: ["evalscope", "perf", "--help"]

  # 开发环境 - 交互式容器
  evalscope-dev:
    build:
      context: .
      dockerfile: Dockerfile
    image: evalscope:latest
    container_name: evalscope-dev
    working_dir: /workspace
    volumes:
      - ./results:/workspace/results
      - ./data:/workspace/data
      - .:/workspace/src
    environment:
      - MODELSCOPE_CACHE=/mnt/workspace/.cache/modelscope
    command: ["bash"]
    stdin_open: true
    tty: true

  # 性能测试服务
  evalscope-test:
    build:
      context: .
      dockerfile: Dockerfile
    image: evalscope:latest
    container_name: evalscope-test
    working_dir: /workspace
    volumes:
      - ./results:/workspace/results
    environment:
      - MODELSCOPE_CACHE=/mnt/workspace/.cache/modelscope
      - PARALLEL=10 25 50
      - NUMBER=100 250 500
      - URL=http://127.0.0.1:8801/v1/chat/completions
      - API=openai
      - MODEL=qwen2.5
      - MAX_TOKENS=1024
      - MIN_TOKENS=1024
    command: [
      "evalscope", "perf",
      "--parallel", "${PARALLEL}",
      "--number", "${NUMBER}",
      "--model", "${MODEL}",
      "--url", "${URL}",
      "--api", "${API}",
      "--dataset", "HC3-Chinese",
      "--max-tokens", "${MAX_TOKENS}",
      "--outputs-dir", "/workspace/results"
    ]
