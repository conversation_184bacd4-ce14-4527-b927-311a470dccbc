# 多架构镜像构建升级说明

## 📋 升级概述

`build-image.sh` 脚本已升级为支持多架构镜像构建，解决了在 ARM64 MacBook 上构建镜像无法在 x86_64 Kubernetes 集群中运行的问题。

## 🔄 主要改进

### 1. 多架构支持

**之前**:
```bash
# 只能构建当前架构的镜像
docker build -t evalscope-perf:v1.0 .
```

**现在**:
```bash
# 默认构建多架构镜像
./build-image.sh

# 支持 linux/amd64 和 linux/arm64 两种架构
```

### 2. 跨平台兼容性

| 开发环境 | 目标集群 | 之前 | 现在 |
|----------|----------|------|------|
| ARM64 MacBook | x86_64 K8s | ❌ 无法运行 | ✅ 完美运行 |
| ARM64 MacBook | ARM64 K8s | ✅ 正常运行 | ✅ 正常运行 |
| x86_64 Linux | x86_64 K8s | ✅ 正常运行 | ✅ 正常运行 |
| x86_64 Linux | ARM64 K8s | ❌ 无法运行 | ✅ 完美运行 |

### 3. 智能回退机制

```bash
# 如果 Docker Buildx 不可用，自动回退到单架构模式
./build-image.sh
# → 检测到 buildx 不可用
# → 自动使用传统 docker build
# → 保持向后兼容
```

### 4. 灵活的构建选项

```bash
# 多架构构建 (默认)
./build-image.sh

# 单架构构建 (向后兼容)
./build-image.sh --single-arch

# 仅构建 AMD64
./build-image.sh --platforms linux/amd64

# 仅构建 ARM64
./build-image.sh --platforms linux/arm64

# 预演模式
./build-image.sh --dry-run

# 仅构建不推送
./build-image.sh --no-push
```

## 🛠️ 技术实现

### Docker Buildx 集成

1. **自动检测**: 检查 Docker Buildx 是否可用
2. **Builder 管理**: 创建专用的 `evalscope-builder` 实例
3. **跨架构模拟**: 使用 QEMU 进行跨架构构建
4. **Manifest 创建**: 自动创建多架构 manifest

### 构建流程

```mermaid
graph TD
    A[开始] --> B{检查 Buildx}
    B -->|可用| C[多架构模式]
    B -->|不可用| D[单架构模式]
    
    C --> E[创建 Builder]
    E --> F[跨架构构建]
    F --> G[推送 Manifest]
    
    D --> H[传统构建]
    H --> I[推送镜像]
    
    G --> J[完成]
    I --> J
```

### 错误处理

- **Buildx 不可用**: 自动回退到单架构模式
- **网络问题**: 提供重试机制
- **权限问题**: 清晰的错误提示和解决方案
- **资源不足**: 建议和优化提示

## 📊 使用统计

### 推荐使用方式

| 场景 | 推荐命令 | 说明 |
|------|----------|------|
| 日常开发 | `./build-image.sh --single-arch --no-push` | 快速本地构建 |
| 功能测试 | `./build-image.sh --no-push` | 多架构构建验证 |
| 生产发布 | `./build-image.sh` | 完整多架构发布 |
| CI/CD | `./build-image.sh --tag $VERSION` | 自动化构建 |

### 性能对比

| 构建类型 | 构建时间 | 镜像大小 | 兼容性 |
|----------|----------|----------|--------|
| 单架构 | ~2 分钟 | 正常 | 单一架构 |
| 多架构 | ~4 分钟 | 相同 | 全架构 |

## 🔧 环境要求

### 必需组件

1. **Docker Desktop 4.0+** (推荐) 或 Docker Engine 20.10+
2. **Docker Buildx** (通常随 Docker Desktop 安装)
3. **足够的磁盘空间** (多架构构建需要更多空间)

### 可选组件

1. **QEMU** (Docker Desktop 自带)
2. **Registry 访问权限** (用于推送镜像)

## 🚀 迁移指南

### 从旧版本迁移

1. **备份现有脚本**:
   ```bash
   cp build-image.sh build-image.sh.backup
   ```

2. **使用新脚本**:
   ```bash
   # 新脚本完全向后兼容
   ./build-image.sh --single-arch  # 等同于旧版本行为
   ```

3. **逐步采用多架构**:
   ```bash
   # 先测试多架构构建
   ./build-image.sh --dry-run
   
   # 确认无误后正式构建
   ./build-image.sh
   ```

### 配置更新

**旧配置**:
```bash
IMAGE_NAME="evalscope-perf"
IMAGE_TAG="v1.0"
REGISTRY="your-registry.com"
```

**新配置** (兼容旧配置):
```bash
# 所有旧配置仍然有效
# 新增多架构相关配置
DEFAULT_PLATFORMS="linux/amd64,linux/arm64"
BUILDER_NAME="evalscope-builder"
```

## 🎯 最佳实践

### 1. 开发阶段

```bash
# 快速迭代 - 使用单架构模式
./build-image.sh --single-arch --no-push

# 本地测试
docker run --rm evalscope-perf:v1.0 --help
```

### 2. 测试阶段

```bash
# 验证多架构构建
./build-image.sh --no-push

# 检查 manifest
docker buildx imagetools inspect evalscope-perf:v1.0
```

### 3. 生产部署

```bash
# 完整多架构构建
./build-image.sh --registry production-registry.com --tag stable

# 验证部署
kubectl set image deployment/evalscope container=production-registry.com/evalscope-perf:stable
```

## 🔍 故障排查

### 常见问题

1. **`exec format error`**
   - 原因: 架构不匹配
   - 解决: 使用多架构镜像

2. **`buildx not found`**
   - 原因: Docker Buildx 未安装
   - 解决: 更新 Docker Desktop 或手动安装

3. **构建超时**
   - 原因: 跨架构构建较慢
   - 解决: 增加超时时间或使用缓存

### 调试命令

```bash
# 检查 Docker 环境
docker version
docker buildx version

# 检查可用架构
docker buildx inspect --bootstrap

# 查看镜像 manifest
docker buildx imagetools inspect your-image:tag
```

## 📈 未来规划

### 短期目标

- [ ] 添加构建缓存优化
- [ ] 支持更多架构 (如 linux/arm/v7)
- [ ] 集成 CI/CD 模板

### 长期目标

- [ ] 支持 OCI 镜像格式
- [ ] 集成安全扫描
- [ ] 自动化性能测试

## 🤝 贡献指南

欢迎提交改进建议和 Bug 报告：

1. **Issue**: 报告问题或建议功能
2. **PR**: 提交代码改进
3. **文档**: 完善使用文档

---

**升级完成！** 🎉

现在您可以在任何架构的开发环境中构建出兼容所有目标 Kubernetes 集群的 EvalScope 性能测试镜像了！
