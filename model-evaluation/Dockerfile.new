FROM python:3.10

# 设置工作目录
WORKDIR /workspace

# 添加标签
LABEL maintainer="evalscope-user"
LABEL description="EvalScope Performance Testing Image with Pre-downloaded HC3-Chinese Dataset"
LABEL version="1.0"

# 更新包管理器并安装必要的系统工具（python:3.10 已包含大部分工具）
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

# 升级 pip 并安装依赖
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir evalscope[all]

# 安装 ModelScope 用于数据集下载
RUN pip install --no-cache-dir modelscope

# 设置 ModelScope 缓存目录
ENV MODELSCOPE_CACHE=/mnt/workspace/.cache/modelscope
RUN mkdir -p $MODELSCOPE_CACHE

# 使用 modelscope 命令行直接下载 HC3-Chinese 数据集
RUN modelscope download --dataset AI-ModelScope/HC3-Chinese --cache_dir /mnt/workspace/.cache/modelscope

# 设置默认命令
CMD ["evalscope", "perf", "--help"]
