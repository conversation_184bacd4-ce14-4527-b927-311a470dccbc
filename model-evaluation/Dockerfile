
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    && rm -rf /var/lib/apt/lists/*

# 升级 pip
RUN pip install --no-cache-dir --upgrade pip

# 安装 EvalScope 性能测试依赖
RUN pip install --no-cache-dir evalscope[all]

# 创建结果输出目录
RUN mkdir -p /tmp/evalscope-results



# 添加标签
LABEL maintainer="evalscope-user"
LABEL description="EvalScope Performance Testing Image"


CMD ["evalscope", "perf", "--help"]
~                                       