
FROM python:3.10

# 设置工作目录
WORKDIR /workspace
# 添加标签
LABEL maintainer="evalscope-user"
LABEL description="EvalScope Performance Testing Image"

RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir evalscope[all]

# 安装 ModelScope 用于数据集下载（使用阿里云镜像源）
RUN pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ modelscope

# 设置 ModelScope 缓存目录
ENV MODELSCOPE_CACHE=/mnt/workspace/.cache/modelscope
RUN mkdir -p $MODELSCOPE_CACHE

# 使用 modelscope 命令行直接下载 HC3-Chinese 数据集
RUN modelscope download --dataset AI-ModelScope/HC3-Chinese --cache_dir /mnt/workspace/.cache/modelscope







# 设置默认命令
CMD ["evalscope", "perf", "--help"]
                                    